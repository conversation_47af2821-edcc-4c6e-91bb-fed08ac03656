# Import necessary modules
import streamlit as st
import pandas as pd
import streamlit_antd_components as sac
from local_components import card_container
from components.utils import get_and_prepare_data, update_totals, format_number_with_spaces, format_number_with_spaces_and_gbp
from components.charts import render_bar_chart, render_stacked_bar_chart, render_pie_chart, render_table
from components.styles import hide_st_style, htmlstr_country

update_info = "2025 Q2p5"
st.set_page_config(page_title=f"Productivity Cost Base {update_info}", layout="wide", page_icon='t.png')
st.markdown(hide_st_style, unsafe_allow_html=True)

metric_style = """
<style>
div[data-testid="stMetric"] {
    background-color: #252f42a9;
    padding: 15px;
    border-radius: 7px;
    border-left: 5px solid #1f77b4;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    margin-top: 5px;  /* Add top margin */
    margin-bottom: 5px;  /* Add bottom margin */
}

[data-testid="block-container"] {
    padding-top: 5px;  /* Add padding to container */
}

div[data-testid="stMetric"]:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    cursor: pointer;
}

div[data-testid="stMetric"] label {
    color: white !important;
    font-weight: bold;
}

div[data-testid="stMetric"] div[data-testid="stMetricValue"] {
    color: white !important;
}
</style>
"""

st.markdown(metric_style, unsafe_allow_html=True)

# Include custom styles
with open("styles.html") as f:
    st.markdown(f'<style>{f.read()}</style>', unsafe_allow_html=True)



# Initialize session state
if 'tab_selection' not in st.session_state:
    st.session_state.tab_selection = 'Bar Chart'
if 'current_level' not in st.session_state:
    st.session_state.current_level = 'model'
if 'current_selection' not in st.session_state:
    st.session_state.current_selection = None
if 'stacked_chart' not in st.session_state:
    st.session_state.stacked_chart = False
if 'data_metric' not in st.session_state:
    st.session_state.data_metric = 'Yearly GBP'
if 'filters' not in st.session_state:
    st.session_state['filters'] = {'Country': [], 'Format': [], 'Store': [], 'model': []}
if 'search_term_act_group' not in st.session_state:
    st.session_state.search_term_act_group = ""
if 'search_term_subop' not in st.session_state:
    st.session_state.search_term_subop = ""
if 'show_all_stores' not in st.session_state:
    st.session_state.show_all_stores = False

def drilldown_handler(event):
    level = len(event['selection'])
    selection = event['selection']
    
    if level == 1:
        st.session_state.current_level = 'activity_group'
        st.session_state.current_selection = [selection[0]]
    elif level == 2:
        st.session_state.current_level = 'suboperation'
        st.session_state.current_selection = [selection[0], selection[1]]
    
def update_extra_columns_for_table():
    extra_columns = []
    if st.session_state['filters']['Country']:
        extra_columns.append('Country')
    if st.session_state['filters']['Format']:
        extra_columns.append('Format')
    if st.session_state['filters']['Store']:
        extra_columns.append('Store')
    return extra_columns

def set_tab_selection(tab):
    st.session_state.tab_selection = tab

with st.sidebar:
    st.image("Tesco_logo.png", use_container_width=True)

tab_selection = sac.tabs([
    sac.TabsItem(label='Bar Chart', icon='bar-chart'),
    sac.TabsItem(label='Pie Chart', icon='pie-chart'),
    sac.TabsItem(label='Table', icon='table'),
], align='center')

if tab_selection == 'Bar Chart':
    set_tab_selection('Bar Chart')
elif tab_selection == 'Pie Chart':
    set_tab_selection('Pie Chart')
elif tab_selection == 'Table':
    set_tab_selection('Table')

selected_tab = st.session_state.tab_selection


with st.sidebar:
    # Add version selector
    version_options = ["2025 Q2p5","2025 Q1p2","2025 Q1", "2024 Q4"]
    selected_version = st.selectbox("Select Version", version_options, index=0, key='selected_version')

    # Update file path based on selected version
    if selected_version == "2025 Q2p5":
        file_path_original = "to_dashboard_25q2_p5"

    elif selected_version == "2025 Q1p2":
        file_path_original = "to_dashboard_25q1_p2_"
    
    elif selected_version == "2025 Q1":
        file_path_original = "to_dashboard_25q1_"
        st.warning(f"Please be noticed that newer version ({update_info}) is available !!", icon="⚠️")

    else:
        file_path_original = "to_dashboard_q4_"
        st.warning(f"Please be noticed that newer version ({update_info}) is available !!", icon="⚠️")

    df = get_and_prepare_data(file_path_original)




def get_filtered_options(df, column, filters):
    filtered_df = df.copy()
    for col, values in filters.items():
        if col != column and values:
            filtered_df = filtered_df[filtered_df[col].isin(values)]
    return filtered_df[column].unique().tolist()

# Function to update country filter in session state
def update_country_filter():
    st.session_state['filters']['Country'] = st.session_state.selected_country

# Function to update format filter in session state
def update_format_filter():
    st.session_state['filters']['Format'] = st.session_state.selected_format

# Function to update store filter in session state
def update_store_filter():
    st.session_state['filters']['Store'] = st.session_state.selected_store

# Function to handle "Show All Stores" checkbox
def update_show_all_stores():
    # This will trigger a rerun and the logic will be handled in handle_show_all_stores_logic
    pass

# Function to update store filter in session state
def update_model_filter():
    st.session_state['filters']['model'] = st.session_state.selected_model

# Function to update data metric in session state
def update_data_metric():
    st.session_state.data_metric = st.session_state.selected_data_metric

# Function to update stacked chart toggle in session state
def update_stacked_chart_toggle():
    st.session_state.stacked_chart = st.session_state.stacked_chart_toggle

# Multiselect filters
country_options = get_filtered_options(df, 'Country', st.session_state['filters'])
format_options = get_filtered_options(df, 'Format', st.session_state['filters'])
store_options = get_filtered_options(df, 'Store', st.session_state['filters'])
model_options = get_filtered_options(df, 'model', st.session_state['filters'])

# Handle "Show All Stores" checkbox logic
def handle_show_all_stores_logic():
    if st.session_state.show_all_stores:
        # When checkbox is checked, select all available stores
        all_stores = get_filtered_options(df, 'Store', {k: v for k, v in st.session_state['filters'].items() if k != 'Store'})
        if set(st.session_state['filters']['Store']) != set(all_stores):
            st.session_state['filters']['Store'] = all_stores
            st.session_state.selected_store = all_stores
            st.rerun()
    else:
        # When checkbox is unchecked, maintain current store selection
        # No action needed as the multiselect will handle the current selection
        pass

# Call the logic function
handle_show_all_stores_logic()

# Filter dataframe based on selections
filtered_df = df.copy()
for col, values in st.session_state['filters'].items():
    if values:
        filtered_df = filtered_df[filtered_df[col].isin(values)]

def update_search_term_act_group():
    st.session_state.search_term_act_group = st.session_state.input_search_term_act_group

def update_search_term_subop():
    st.session_state.search_term_subop = st.session_state.input_search_term_subop

with st.sidebar:
    sac.divider(label='main selectors', icon='gear', align='center', color='gray')
    st.write("Apply filters in any order 👇")

    selected_country = st.multiselect(
        'Select Country', 
        country_options, 
        default=st.session_state['filters']['Country'], 
        key='selected_country', 
        on_change=update_country_filter
    )
    selected_format = st.multiselect(
        'Select Format', 
        format_options, 
        default=st.session_state['filters']['Format'], 
        key='selected_format', 
        on_change=update_format_filter
    )

    # Store selection (always visible)
    selected_store = st.multiselect(
        'Select Store',
        store_options,
        default=st.session_state['filters']['Store'],
        key='selected_store',
        on_change=update_store_filter
    )


    selected_model = st.multiselect(
        'Select Model',
        model_options, 
        default=st.session_state['filters']['model'], 
        key='selected_model', 
        on_change=update_model_filter
    )

    data_metric = st.radio(
        label="Select Metric",
        options=["Yearly GBP", "Weekly Hours"],
        index=["Yearly GBP", "Weekly Hours"].index(st.session_state.data_metric),
        key='selected_data_metric',
        horizontal=True,
        on_change=update_data_metric
    )

    sac.divider(label='other selectors', icon='filter', align='center', color='gray')
    
    st.text_input("Search in 'Activity Groups'", value=st.session_state.search_term_act_group, key='input_search_term_act_group', on_change=update_search_term_act_group)
    st.text_input("Search in 'Suboperations'", value=st.session_state.search_term_subop, key='input_search_term_subop', on_change=update_search_term_subop)
    
    filtered_items_act_group = [item for item in filtered_df['Activity Group'].unique().tolist() if st.session_state.search_term_act_group.lower() in item.lower()]
    filtered_items_subop = [item for item in filtered_df['Suboperation'].unique().tolist() if st.session_state.search_term_subop.lower() in item.lower()]
    
    if filtered_items_act_group:
        filtered_df = filtered_df[filtered_df['Activity Group'].isin(filtered_items_act_group)]
    if filtered_items_subop:
        filtered_df = filtered_df[filtered_df['Suboperation'].isin(filtered_items_subop)]

model_summary = filtered_df.groupby('model', observed=True)[['Weekly Hours', 'Yearly GBP']].sum().reset_index().sort_values(by=st.session_state.data_metric, ascending=False)
activity_group_summary = filtered_df.groupby(['model', 'Activity Group'], observed=True)[['Weekly Hours', 'Yearly GBP']].sum().reset_index().sort_values(by=st.session_state.data_metric, ascending=False)
suboperation_summary = filtered_df.groupby(['model', 'Activity Group', 'Suboperation'], observed=True)[['Weekly Hours', 'Yearly GBP']].sum().reset_index().sort_values(by=st.session_state.data_metric, ascending=False)

# Apply extra columns from filter to Table
extra_columns_for_table = update_extra_columns_for_table()
table_filter = filtered_df.groupby(extra_columns_for_table + ['model', 'Activity Group', 'Suboperation'], observed=True)[['Weekly Hours', 'Yearly GBP']].sum().reset_index()

if st.session_state.current_selection:
    total_weekly_hours, total_yearly_gbp = update_totals(st.session_state.current_selection[-1], st.session_state.current_level, model_summary, activity_group_summary, suboperation_summary)
else:
    total_weekly_hours = model_summary['Weekly Hours'].sum()
    total_yearly_gbp = model_summary['Yearly GBP'].sum()

with card_container(key="info"):
    cols = st.columns(3)
    with cols[0]:
        st.metric(label="Budgeted Update:", value=update_info)
    with cols[2]:
        st.metric(label="Total Weekly Hours:", value=f"{format_number_with_spaces(total_weekly_hours)}")
    with cols[1]:
        st.metric(label="Total Yearly GBP:", value=f"{format_number_with_spaces_and_gbp(total_yearly_gbp)}")

with card_container(key="GBP_countries"):
    cols1, cols2 = st.columns([5,1])
    with cols1:
        expander_label = "Show me the Yearly GBP by Countries" if st.session_state.data_metric == "Yearly GBP" else "Show me the Weekly Hours by Countries"
        with st.expander(expander_label, expanded=True):
            cols = st.columns(3)
            with cols[0]:
                st.markdown(htmlstr_country("CZ", filtered_df), unsafe_allow_html=True)
            with cols[2]:
                st.markdown(htmlstr_country("SK", filtered_df), unsafe_allow_html=True)
            with cols[1]:
                st.markdown(htmlstr_country("HU", filtered_df), unsafe_allow_html=True)
    with cols2:
        stacked_chart_toggle = st.toggle(
            label="Show Stacked Bar Chart", 
            value=st.session_state.stacked_chart, 
            key='stacked_chart_toggle', 
            on_change=update_stacked_chart_toggle
        )

if selected_tab == "Bar Chart":
    with card_container(key="chart1"):
        if st.session_state.stacked_chart:
            activity_group_summary_country = filtered_df.groupby(['model', 'Activity Group', 'Country'], observed=True)[['Weekly Hours', 'Yearly GBP']].sum().reset_index().sort_values(by=st.session_state.data_metric, ascending=False)
            suboperation_summary_country = filtered_df.groupby(['model', 'Activity Group', 'Suboperation', 'Country'], observed=True)[['Weekly Hours', 'Yearly GBP']].sum().reset_index().sort_values(by=st.session_state.data_metric, ascending=False)

            render_stacked_bar_chart(filtered_df, model_summary, activity_group_summary_country, suboperation_summary_country)
        else:
            render_bar_chart(filtered_df, model_summary, activity_group_summary, suboperation_summary)
elif selected_tab == "Pie Chart":
    with card_container(key="chart2"):
        render_pie_chart(filtered_df, model_summary, activity_group_summary, suboperation_summary)
elif selected_tab == "Table":
    with card_container(key="chart3"):
        render_table(table_filter)

# st.markdown("##")
# st.markdown("##")
# st.write("© Copyright 2024 Peter Hrubos.  All rights reserved.")
